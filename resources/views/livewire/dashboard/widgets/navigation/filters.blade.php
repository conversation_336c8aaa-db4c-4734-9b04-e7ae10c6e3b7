<div>
    <select
            class="custom-select border-none mt-2 mt-sm-0 tabs-itemBox-Style"
            id="longday-livewire"
            onchange="redirectWithParam(this)"
    >
        <option value="">{{ __('action.lta_choose_days') }}</option>
        @foreach($lta_days as $days)
            <option value="{{ $days->days }}" @if(request()->get('longDay') == $days->days) selected @endif>
                {{ $days->days }} {{ __('action.lta_days') }}
            </option>
        @endforeach
    </select>

    @once
        <style>
            /* Looping opacity animation */
            @keyframes pulseOpacity {
                0%   { opacity: 1; }
                50%  { opacity: 0.4; }
                100% { opacity: 1; }
            }

            .opacity-pulse {
                animation: pulseOpacity 1.5s ease-in-out infinite;
                pointer-events: none;
                cursor: not-allowed;
            }
            .grid-stack>.grid-stack-item>.grid-stack-item-content {
                margin: 0;
                position: absolute;
                width: auto;
                overflow-x: hidden;
                overflow-y: hidden !important
            }
        </style>

        <script>
            function redirectWithParam(selectElement) {
                const value = selectElement.value;
                const currentParams = new URLSearchParams(window.location.search);
                const currentLongDay = currentParams.get('longDay');

                if (currentLongDay !== value) {
                    // Add pulsing opacity animation and disable
                    selectElement.classList.add('opacity-pulse');
                    selectElement.disabled = true;

                    const dashboardUrl = "{{ request()->attributes->get(\App\Enums\RequestAttributes::DASHBOARD_URL->value) }}";
                    const url = new URL(dashboardUrl);

                    if (value) {
                        url.searchParams.set('longDay', value);
                    } else {
                        url.searchParams.delete('longDay');
                    }

                    // Optional small delay to let animation start before redirect
                    setTimeout(() => {
                        window.location.href = url.toString();
                    }, 100);
                }
            }
        </script>
    @endonce
</div>